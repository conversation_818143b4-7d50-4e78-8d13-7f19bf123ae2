// TrueBDC CRM Automation Suite - Popup Script

class TrueBDCPopup {
    constructor() {
        this.currentTab = 'eleads';
        this.settings = {};
        this.init();
    }

    async init() {
        await this.loadSettings();
        this.setupEventListeners();
        this.updateUI();
        this.loadScriptStates();
    }

    setupEventListeners() {
        // Tab navigation
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Script toggles
        document.querySelectorAll('input[data-script]').forEach(toggle => {
            toggle.addEventListener('change', (e) => {
                this.toggleScript(e.target.dataset.script, e.target.checked);
            });
        });

        // Settings
        document.getElementById('save-settings').addEventListener('click', () => {
            this.saveSettings();
        });

        document.getElementById('reset-settings').addEventListener('click', () => {
            this.resetSettings();
        });




    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');

        this.currentTab = tabName;
    }

    async toggleScript(scriptName, enabled) {
        try {
            // Save script state
            await chrome.storage.local.set({
                [`script_${scriptName}`]: enabled
            });

            // Send message to content script
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (tab) {
                chrome.tabs.sendMessage(tab.id, {
                    action: 'toggleScript',
                    script: scriptName,
                    enabled: enabled
                });
            }

            this.updateStatus(`${scriptName} ${enabled ? 'enabled' : 'disabled'}`);
        } catch (error) {
            console.error('Error toggling script:', error);
            this.updateStatus('Error updating script', 'error');
        }
    }

    async loadScriptStates() {
        try {
            const scripts = [
                // 'dynamicTabTitle', // Removed - handled by popup title changer
                'bypassRefresh',
                'clickToCall',
                'tabToPopup',
                'autoRefresh',
                'callingText',
                'autoNavigation',
                'autoCloseReleaseNotes'
            ];

            for (const script of scripts) {
                const result = await chrome.storage.local.get(`script_${script}`);
                const enabled = result[`script_${script}`] || false;

                // Update eLeads toggles
                const eleadsToggle = document.getElementById(script.replace(/([A-Z])/g, '-$1').toLowerCase());
                if (eleadsToggle) {
                    eleadsToggle.checked = enabled;
                }
            }

            // Handle VinSo scripts separately
            const vinsoClickToCallResult = await chrome.storage.local.get('script_vinsoClickToCall');
            const vinsoClickToCallEnabled = vinsoClickToCallResult['script_vinsoClickToCall'] || false;
            const vinsoClickToCallToggle = document.getElementById('vinso-click-to-call');
            if (vinsoClickToCallToggle) {
                vinsoClickToCallToggle.checked = vinsoClickToCallEnabled;
            }

            const vinsoRepNameResult = await chrome.storage.local.get('script_vinsoRepNameAutomation');
            const vinsoRepNameEnabled = vinsoRepNameResult['script_vinsoRepNameAutomation'] || false;
            const vinsoRepNameToggle = document.getElementById('vinso-rep-name-automation');
            if (vinsoRepNameToggle) {
                vinsoRepNameToggle.checked = vinsoRepNameEnabled;
            }
        } catch (error) {
            console.error('Error loading script states:', error);
        }
    }

    async loadSettings() {
        try {
            const result = await chrome.storage.local.get('settings');
            this.settings = result.settings || {
                dealershipName: '',
                agentName: 'Rep',
                // Airtable settings - temporarily disabled but kept in storage
                airtableApiKey: '',
                airtableBaseId: 'appexR9tFKGHjSWNE',
                refreshInterval: 5,
                // Popup window settings
                popupWidth: 1200,
                popupHeight: 800,
                // popupAlwaysOnTop: false, // REMOVED - Chrome extension limitations
                // Auto Navigation - managed by script toggle only
            };

            // Update UI with loaded settings
            document.getElementById('dealership-name').value = this.settings.dealershipName || '';
            document.getElementById('agent-name').value = this.settings.agentName || 'Rep';
            // Airtable fields - temporarily disabled
            // document.getElementById('airtable-api-key').value = this.settings.airtableApiKey;
            // document.getElementById('airtable-base-id').value = this.settings.airtableBaseId;
            document.getElementById('refresh-interval').value = this.settings.refreshInterval || 5;

            // Popup window settings
            document.getElementById('popup-width').value = this.settings.popupWidth || 1200;
            document.getElementById('popup-height').value = this.settings.popupHeight || 800;
            // Always on top removed - Chrome extension limitations

            // Auto Navigation - managed by script toggle only

            // Load and display current saved popup configurations
            await this.loadCurrentPopupDimensions();
        } catch (error) {
            console.error('Error loading settings:', error);
        }
    }

    async saveSettings() {
        try {
            this.settings = {
                dealershipName: document.getElementById('dealership-name').value,
                agentName: document.getElementById('agent-name').value,
                // Keep existing Airtable settings in storage (temporarily disabled in UI)
                airtableApiKey: this.settings.airtableApiKey || '',
                airtableBaseId: this.settings.airtableBaseId || 'appexR9tFKGHjSWNE',
                refreshInterval: parseInt(document.getElementById('refresh-interval').value),
                // Popup window settings
                popupWidth: parseInt(document.getElementById('popup-width').value),
                popupHeight: parseInt(document.getElementById('popup-height').value),
                // popupAlwaysOnTop removed - Chrome extension limitations
                // Auto Navigation - managed by script toggle only
            };

            await chrome.storage.local.set({ settings: this.settings });

            // Update default popup configuration in Tab to Popup converter
            await this.updateDefaultPopupConfig();

            // Send updated settings to content scripts
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (tab) {
                chrome.tabs.sendMessage(tab.id, {
                    action: 'updateSettings',
                    settings: this.settings
                });
            }

            this.updateStatus('Settings saved successfully');
        } catch (error) {
            console.error('Error saving settings:', error);
            this.updateStatus('Error saving settings', 'error');
        }
    }

    async resetSettings() {
        if (confirm('Are you sure you want to reset all settings to defaults?')) {
            try {
                await chrome.storage.local.remove('settings');
                await this.loadSettings();
                this.updateStatus('Settings reset to defaults');
            } catch (error) {
                console.error('Error resetting settings:', error);
                this.updateStatus('Error resetting settings', 'error');
            }
        }
    }





    updateStatus(message, type = 'success') {
        const statusElement = document.getElementById('status-message');
        statusElement.textContent = message;
        statusElement.style.color = type === 'error' ? '#dc3545' : '#28a745';
        
        setTimeout(() => {
            statusElement.textContent = 'Ready';
            statusElement.style.color = '#28a745';
        }, 3000);
    }

    async loadCurrentPopupDimensions() {
        try {
            // Load saved popup configurations from Tab to Popup converter
            const result = await chrome.storage.local.get('popupConfigs');
            const popupConfigs = result.popupConfigs || {};

            const currentDimensionsDiv = document.getElementById('current-dimensions');

            if (Object.keys(popupConfigs).length === 0) {
                currentDimensionsDiv.innerHTML = '<small>No saved popup configurations yet</small>';
                return;
            }

            let dimensionsHTML = '<small><strong>Currently Saved Configurations:</strong></small><br>';

            for (const [urlKey, config] of Object.entries(popupConfigs)) {
                const displayName = urlKey === 'weblink' ? 'eLeadCRM Weblink' : urlKey;
                dimensionsHTML += `
                    <div class="dimension-info">
                        <span>${displayName}:</span>
                        <span>${config.width}×${config.height} at (${config.left}, ${config.top})</span>
                    </div>
                `;
            }

            currentDimensionsDiv.innerHTML = dimensionsHTML;
        } catch (error) {
            console.error('Error loading popup dimensions:', error);
        }
    }

    async updateDefaultPopupConfig() {
        try {
            // Update the default configuration used by Tab to Popup converter
            const defaultConfig = {
                width: this.settings.popupWidth,
                height: this.settings.popupHeight,
                left: Math.round((screen.width - this.settings.popupWidth) / 2),
                top: Math.round((screen.height - this.settings.popupHeight) / 2)
                // alwaysOnTop removed - Chrome extension limitations
            };

            // Send message to content scripts to update default config
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (tab) {
                chrome.tabs.sendMessage(tab.id, {
                    action: 'updateDefaultPopupConfig',
                    config: defaultConfig
                });
            }
        } catch (error) {
            console.error('Error updating default popup config:', error);
        }
    }

    updateUI() {
        // Any additional UI updates can go here
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.trueBDCPopup = new TrueBDCPopup();
});
