// TrueBDC CRM Automation Suite - VinSolutions Click to Call
// Specialized click-to-call implementation for VinSolutions CRM system

console.log('VinSo Click-to-Call script loaded!', {
    url: window.location.href,
    timestamp: new Date().toISOString()
});

class VinSoClickToCall {
    constructor(settings = {}) {
        console.log('VinSo Click-to-Call constructor called!', {
            settings,
            url: window.location.href
        });

        this.settings = settings;
        this.isActive = false;
        this.lastClickedNumber = null;
        this.lastClickTime = 0;
        // Enhanced call tracking thresholds
        this.consecutiveCallThreshold = 1000 * 60 * 10; // 10 minutes for consecutive call detection
        this.callCountResetThreshold = 1000 * 60 * 30; // 30 minutes for count reset
        this.dailyResetThreshold = 1000 * 60 * 60 * 24; // 24 hours for daily reset
        this.phoneIcons = new Map();
        this.observer = null;
        this.lastHoveredIcon = null; // For clipboard copy functionality
        this.maxPhones = 4; // Maximum number of phones to process
        this.isProcessingComplete = false; // Flag to stop processing once done
        this.observerSetup = false; // Prevent multiple observers

        this.init();
    }

    init() {
        try {
            const frameContext = TrueBDCUtils.getFrameContext();
            const url = window.location.href;

            TrueBDCUtils.log('Initializing VinSolutions Click to Call', {
                frameContext: frameContext,
                url: url,
                isVinSolutionsPage: this.isVinSolutionsPage()
            });

            // Check if we're on a VinSolutions page
            if (this.isVinSolutionsPage()) {
                this.setupPhoneDetection();
                this.isActive = true;

                TrueBDCUtils.log('VinSolutions Click to Call activated', {
                    frameContext: frameContext,
                    url: url
                });
                TrueBDCUtils.logActivity('vinso_click_to_call_activated', {
                    url: url,
                    frameContext: frameContext
                });
            } else {
                TrueBDCUtils.log('VinSolutions Click to Call not activated - not VinSolutions page', {
                    url: url,
                    frameContext: frameContext,
                    expectedPatterns: [
                        'vinsolutions.app.coxautoinc.com/CarDashboard/Pages/CRM/CustomerDashboard.aspx',
                        'apps.vinmanager.com/CarDashboard/Pages/CRM/CustomerDashboard.aspx'
                    ]
                });
            }
        } catch (error) {
            TrueBDCUtils.error('Failed to initialize VinSolutions Click to Call', error);
        }
    }

    isVinSolutionsPage() {
        const url = window.location.href;
        const vinSolutionsPatterns = [
            /vinsolutions\.app\.coxautoinc\.com/i,
            /apps\.vinmanager\.com/i,
            /.*\.vinsolutions\.com/i
        ];

        const isMatch = vinSolutionsPatterns.some(pattern => pattern.test(url));

        console.log('VinSo URL Check:', {
            url,
            patterns: vinSolutionsPatterns.map(p => p.toString()),
            isMatch
        });

        return isMatch;
    }

    setupPhoneDetection() {
        // Initial scan for phone numbers
        this.scanForVinSoPhoneNumbers();

        // Set up mutation observer only if not already set up
        if (!this.observerSetup) {
            this.setupMutationObserver();
        }

        // Periodic scan as fallback (matching Tampermonkey script)
        // Only if we haven't found all phones yet
        if (!this.isProcessingComplete) {
            this.scanInterval = setInterval(() => {
                if (this.isProcessingComplete || this.phoneIcons.size >= this.maxPhones) {
                    this.stopScanning();
                    return;
                }
                this.scanForVinSoPhoneNumbers();
            }, 3000); // Every 3 seconds for VinSolutions

            // Stop periodic scanning after 3 minutes to prevent indefinite execution
            setTimeout(() => {
                this.stopScanning();
            }, 180000); // 3 minutes
        }
    }

    stopScanning() {
        if (this.scanInterval) {
            clearInterval(this.scanInterval);
            this.scanInterval = null;
        }
        this.isProcessingComplete = true;

        console.log('VinSo Click-to-Call: Stopped scanning', {
            phonesFound: this.phoneIcons.size,
            maxPhones: this.maxPhones
        });

        TrueBDCUtils.log('VinSolutions Click-to-Call scanning stopped', {
            phonesFound: this.phoneIcons.size,
            maxPhones: this.maxPhones
        });
    }

    setupMutationObserver() {
        if (this.observerSetup) {
            return; // Already set up
        }

        this.observerSetup = true;

        const config = { childList: true, subtree: true };
        const callback = (mutationsList) => {
            // Stop if we've found enough phones or processing is complete
            if (this.isProcessingComplete || this.phoneIcons.size >= this.maxPhones) {
                return;
            }

            for (let mutation of mutationsList) {
                if (mutation.type === 'childList') {
                    // Debounce the scanning to avoid excessive calls
                    clearTimeout(this.scanTimeout);
                    this.scanTimeout = setTimeout(() => {
                        if (!this.isProcessingComplete && this.phoneIcons.size < this.maxPhones) {
                            this.scanForVinSoPhoneNumbers();
                        }
                    }, 500);
                }
            }
        };

        // Target the deal-details container specifically (from Tampermonkey script)
        const targetNode = document.querySelector('.deal-details');
        if (targetNode) {
            this.observer = new MutationObserver(callback);
            this.observer.observe(targetNode, config);
            TrueBDCUtils.log('VinSolutions mutation observer set up on .deal-details');
        } else {
            // Fallback to document.body if deal-details not found
            this.observer = new MutationObserver(callback);
            this.observer.observe(document.body, config);
            TrueBDCUtils.log('VinSolutions mutation observer set up on document.body (fallback)');
        }
    }

    scanForVinSoPhoneNumbers() {
        try {
            // Stop if we've found enough phones or processing is complete
            if (this.isProcessingComplete || this.phoneIcons.size >= this.maxPhones) {
                this.stopScanning();
                return;
            }

            // VinSolutions-specific phone number detection (from Tampermonkey script)
            this.scanVinSolutionsPhoneDetails();
            this.scanGenericPhoneNumbers();

            // Check if we've found enough phones to stop
            if (this.phoneIcons.size >= this.maxPhones) {
                this.stopScanning();
            }

        } catch (error) {
            TrueBDCUtils.error('Error scanning for VinSolutions phone numbers', error);
        }
    }

    scanVinSolutionsPhoneDetails() {
        // Target the actual VinSolutions customer detail elements
        const customerDetailSpans = document.querySelectorAll('span[id*="CustomerDetail"], span.CustomerInfo_CustomerDetail');

        TrueBDCUtils.log('Scanning VinSolutions customer details', {
            foundElements: customerDetailSpans.length,
            elements: Array.from(customerDetailSpans).map(el => ({
                id: el.id,
                className: el.className,
                textContent: el.textContent.substring(0, 100)
            }))
        });

        // Also log to console for easy debugging
        console.log('VinSo Click-to-Call: Found', customerDetailSpans.length, 'customer detail elements');
        customerDetailSpans.forEach((el, index) => {
            console.log(`Element ${index + 1}:`, {
                id: el.id,
                className: el.className,
                textContent: el.textContent.substring(0, 200)
            });
        });

        customerDetailSpans.forEach(span => {
            if (!span.dataset.vinsoConverted) {
                // Mark as processed to avoid re-processing
                span.dataset.vinsoConverted = true;

                // Extract phone numbers from text content
                this.extractPhoneNumbersFromElement(span);
            }
        });

        // Also scan for the old Tampermonkey targets as fallback
        const phoneSpans = document.querySelectorAll('span[analyticsdetect="DealCard|Navigate|Phone"].phone-detail');
        phoneSpans.forEach(span => {
            if (!span.dataset.vinsoConverted) {
                span.dataset.vinsoConverted = true;
                const originalPhoneNumber = span.textContent.trim();
                const cleanNumber = this.cleanPhoneNumber(originalPhoneNumber);
                if (this.isValidPhoneNumber(cleanNumber)) {
                    this.addVinSoClickToCallIcon(span, originalPhoneNumber, cleanNumber);
                }
            }
        });
    }

    extractPhoneNumbersFromElement(element) {
        // Stop if we've found enough phones
        if (this.phoneIcons.size >= this.maxPhones) {
            return;
        }

        const text = element.innerHTML;

        // Look for phone numbers in format "H: (*************" and "C: (*************"
        const phonePatterns = [
            /([HC]:|Home:|Cell:|Work:|Mobile:)\s*(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})/gi,
            /(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})/g
        ];

        let phonesFoundInElement = 0;

        phonePatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(text)) !== null && this.phoneIcons.size < this.maxPhones) {
                const fullMatch = match[0];
                const phoneNumber = match[2] || match[1]; // Get the phone number part
                const cleanNumber = this.cleanPhoneNumber(phoneNumber);

                if (this.isValidPhoneNumber(cleanNumber) && !this.phoneIcons.has(cleanNumber)) {
                    // Create a wrapper for this specific phone number
                    this.addPhoneNumberToElement(element, phoneNumber, cleanNumber, fullMatch);
                    phonesFoundInElement++;

                    TrueBDCUtils.log('Found VinSolutions phone number', {
                        fullMatch,
                        phoneNumber,
                        cleanNumber,
                        elementId: element.id,
                        totalFound: this.phoneIcons.size
                    });

                    // Stop if we've reached the limit
                    if (this.phoneIcons.size >= this.maxPhones) {
                        break;
                    }
                }
            }
        });

        console.log(`VinSo Click-to-Call: Found ${phonesFoundInElement} phones in element, total: ${this.phoneIcons.size}/${this.maxPhones}`);
    }

    addPhoneNumberToElement(element, originalFormat, cleanNumber, fullMatch) {
        // Check if we already added an icon for this number or reached limit
        const iconId = `vinso-phone-icon-${cleanNumber}`;
        if (document.getElementById(iconId) || this.phoneIcons.has(cleanNumber) || this.phoneIcons.size >= this.maxPhones) {
            return;
        }

        // Replace the phone number text with phone number + icon
        const currentHTML = element.innerHTML;
        const phoneWithIcon = `${fullMatch} <a id="${iconId}" href="tel:+1${cleanNumber}" class="vinso-phone-icon" title="Click to call ${originalFormat}" data-phone="${cleanNumber}" data-original="${originalFormat}" style="cursor: pointer; margin-left: 5px; text-decoration: none; color: inherit; font-size: 14px;">📞</a>`;

        const newHTML = currentHTML.replace(fullMatch, phoneWithIcon);
        element.innerHTML = newHTML;

        // Add click handler to the new icon
        const icon = document.getElementById(iconId);
        if (icon) {
            icon.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.handleVinSoPhoneClick(cleanNumber, originalFormat, e);
            });

            // Add hover handlers for copy functionality
            icon.addEventListener('mouseover', () => {
                this.lastHoveredIcon = icon;
                document.addEventListener('keydown', this.handleCopy.bind(this));
            });

            icon.addEventListener('mouseout', () => {
                document.removeEventListener('keydown', this.handleCopy.bind(this));
                this.lastHoveredIcon = null;
            });

            this.phoneIcons.set(cleanNumber, icon);

            console.log(`VinSo Click-to-Call: Added phone icon for ${cleanNumber} (${this.phoneIcons.size}/${this.maxPhones})`);
        }
    }



    scanGenericPhoneNumbers() {
        // Generic phone number scanning for VinSolutions pages
        const phonePatterns = [
            /\b\d{3}[-.\s]?\d{3}[-.\s]?\d{4}\b/g, // ************, ************, ************
            /\(\d{3}\)\s?\d{3}[-.\s]?\d{4}/g,     // (*************
            /\b\d{10}\b/g                         // 1234567890
        ];

        const textNodes = this.getTextNodes(document.body);
        
        textNodes.forEach(node => {
            const text = node.textContent;
            phonePatterns.forEach(pattern => {
                const matches = text.match(pattern);
                if (matches) {
                    matches.forEach(match => {
                        const cleanNumber = this.cleanPhoneNumber(match);
                        if (this.isValidPhoneNumber(cleanNumber)) {
                            this.addVinSoClickToCallIcon(node, match, cleanNumber);
                        }
                    });
                }
            });
        });
    }



    getTextNodes(element) {
        const textNodes = [];
        const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_TEXT,
            {
                acceptNode: (node) => {
                    // Skip script and style elements
                    const parent = node.parentElement;
                    if (parent && (parent.tagName === 'SCRIPT' || parent.tagName === 'STYLE')) {
                        return NodeFilter.FILTER_REJECT;
                    }
                    
                    // Skip if already processed
                    if (parent && parent.querySelector('.vinso-phone-icon')) {
                        return NodeFilter.FILTER_REJECT;
                    }
                    
                    return NodeFilter.FILTER_ACCEPT;
                }
            }
        );

        let node;
        while (node = walker.nextNode()) {
            textNodes.push(node);
        }

        return textNodes;
    }

    addVinSoClickToCallIcon(targetElement, originalFormat, cleanNumber) {
        try {
            // Check if icon already exists for this number
            const iconId = `vinso-phone-icon-${cleanNumber}`;
            if (document.getElementById(iconId)) return;

            // Create click-to-call icon
            const icon = TrueBDCUtils.createElement('a', {
                id: iconId,
                href: `tel:+1${cleanNumber}`,
                class: 'vinso-phone-icon',
                title: 'Click to call',
                'data-phone': cleanNumber,
                'data-original': originalFormat
            });

            // Use telephone emoji as placeholder (as requested)
            icon.innerHTML = '📞';
            
            // Add styling
            Object.assign(icon.style, {
                cursor: 'pointer',
                marginLeft: '5px',
                textDecoration: 'none',
                color: 'inherit',
                fontSize: '14px'
            });

            // Add click handler
            icon.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.handleVinSoPhoneClick(cleanNumber, originalFormat, e);
            });

            // Add hover handlers for copy functionality
            icon.addEventListener('mouseover', () => {
                this.lastHoveredIcon = icon;
                document.addEventListener('keydown', this.handleCopy.bind(this));
            });

            icon.addEventListener('mouseout', () => {
                document.removeEventListener('keydown', this.handleCopy.bind(this));
                this.lastHoveredIcon = null;
            });

            // Insert icon after the target element
            if (targetElement.nodeType === Node.TEXT_NODE) {
                targetElement.parentNode.insertBefore(icon, targetElement.nextSibling);
            } else {
                targetElement.appendChild(icon);
            }
            
            this.phoneIcons.set(cleanNumber, icon);

        } catch (error) {
            TrueBDCUtils.error('Error adding VinSolutions click-to-call icon', error);
        }
    }

    handleCopy(e) {
        if (this.lastHoveredIcon && e.ctrlKey && (e.key === 'c' || e.key === 'C')) {
            e.preventDefault();
            const originalFormat = this.lastHoveredIcon.getAttribute('data-original');
            navigator.clipboard.writeText(originalFormat).then(() => {
                this.showModernModal(this.lastHoveredIcon, {
                    type: 'success',
                    title: 'Copied!',
                    message: `Phone number ${originalFormat} copied to clipboard`
                });
            }).catch(err => {
                TrueBDCUtils.error('Error copying text to clipboard', err);
                this.showModernModal(this.lastHoveredIcon, {
                    type: 'error',
                    title: 'Copy Failed',
                    message: 'Unable to copy phone number to clipboard'
                });
            });
        }
    }

    async handleVinSoPhoneClick(phoneNumber, originalFormat, event) {
        try {
            const now = Date.now();
            
            // Update click count and check for duplicates
            const shouldProceed = await this.updateClickCount(phoneNumber, event);
            if (!shouldProceed) return;

            // Note: Call tracking modal is shown in updateClickCount method

            // Initiate call
            window.open(`tel:+1${phoneNumber}`, '_self');

            // Log activity
            TrueBDCUtils.logActivity('vinso_phone_call_initiated', {
                phoneNumber: phoneNumber,
                originalFormat: originalFormat,
                timestamp: new Date().toISOString()
            });

            TrueBDCUtils.log('VinSolutions phone call initiated', { 
                phoneNumber: phoneNumber,
                originalFormat: originalFormat 
            });

        } catch (error) {
            TrueBDCUtils.error('Error handling VinSolutions phone click', error);
            this.showModernModal(event.target, {
                type: 'error',
                title: 'Call Failed',
                message: 'Unable to initiate phone call. Please try again.'
            });
        }
    }

    async updateClickCount(phoneNumber, event) {
        const now = Date.now();
        const countKey = `vinso_dialCount_${phoneNumber}`;
        const timestampKey = `vinso_dialTime_${phoneNumber}`;

        try {
            // Get current count and timestamp from storage
            const result = await chrome.storage.local.get([countKey, timestampKey]);
            let currentCount = parseInt(result[countKey], 10) || 0;
            let lastCallTime = parseInt(result[timestampKey], 10) || 0;

            // Determine if we should reset the count based on time thresholds
            const timeSinceLastCall = now - lastCallTime;
            const timeSinceLastConsecutive = now - this.lastClickTime;

            // Reset logic with multiple time thresholds
            if (this.shouldResetCallCount(phoneNumber, now, lastCallTime, timeSinceLastCall)) {
                currentCount = 1;
                console.log(`VinSo Call Tracking: Reset count for ${phoneNumber} due to time threshold`);
            } else if (phoneNumber !== this.lastClickedNumber ||
                       timeSinceLastConsecutive > this.consecutiveCallThreshold) {
                // Different number or enough time passed for consecutive calls
                currentCount = 1;
            } else {
                // Same number, within consecutive threshold
                currentCount++;

                // Show confirmation for 3rd consecutive call
                if (currentCount === 3) {
                    const shouldProceed = await this.showCallConfirmationModal(phoneNumber, event);
                    if (!shouldProceed) {
                        this.showModernModal(event.target, {
                            type: 'cancelled',
                            title: 'Call Cancelled',
                            message: `Call to ${TrueBDCUtils.formatPhoneNumber(phoneNumber)} was cancelled`,
                            phoneNumber: phoneNumber
                        });
                        return false;
                    }
                }
            }

            // Save updated count and timestamp
            await chrome.storage.local.set({
                [countKey]: currentCount,
                [timestampKey]: now
            });

            // Update tracking variables
            this.lastClickedNumber = phoneNumber;
            this.lastClickTime = now;

            // Show modern call tracking modal
            this.showModernModal(event.target, {
                type: 'success',
                title: 'Call Initiated',
                message: `Calling ${TrueBDCUtils.formatPhoneNumber(phoneNumber)}`,
                phoneNumber: phoneNumber,
                callCount: currentCount,
                isConsecutive: currentCount > 1 && timeSinceLastConsecutive <= this.consecutiveCallThreshold
            });

            return true;

        } catch (error) {
            TrueBDCUtils.error('Error updating VinSolutions click count', error);
            return true; // Proceed with call even if count tracking fails
        }
    }

    shouldResetCallCount(phoneNumber, now, lastCallTime, timeSinceLastCall) {
        // No previous call recorded
        if (!lastCallTime) return true;

        // Daily reset (24 hours)
        if (timeSinceLastCall > this.dailyResetThreshold) {
            return true;
        }

        // Count reset threshold (30 minutes)
        if (timeSinceLastCall > this.callCountResetThreshold) {
            return true;
        }

        return false;
    }

    async showCallConfirmationModal(phoneNumber, event) {
        return new Promise((resolve) => {
            // Create modal backdrop
            const backdrop = TrueBDCUtils.createElement('div', {
                class: 'vinso-modal-backdrop'
            }, {
                position: 'fixed',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                backdropFilter: 'blur(4px)',
                zIndex: '999998',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                opacity: '0',
                transition: 'opacity 0.3s ease'
            });

            // Create modal container
            const modal = TrueBDCUtils.createElement('div', {
                class: 'vinso-confirmation-modal'
            }, {
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)',
                padding: '24px',
                maxWidth: '400px',
                width: '90%',
                transform: 'scale(0.9)',
                transition: 'transform 0.3s ease',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
            });

            // Create modal content
            modal.innerHTML = `
                <div style="text-align: center; margin-bottom: 20px;">
                    <div style="width: 48px; height: 48px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; margin: 0 auto 16px; display: flex; align-items: center; justify-content: center;">
                        <span style="font-size: 24px; color: white;">⚠️</span>
                    </div>
                    <h3 style="margin: 0 0 8px 0; color: #2d3748; font-size: 18px; font-weight: 600;">Confirm Call</h3>
                    <p style="margin: 0; color: #4a5568; font-size: 14px; line-height: 1.5;">You're about to call this number for the 3rd time in a row.</p>
                </div>

                <div style="background: #f7fafc; border-radius: 8px; padding: 16px; margin-bottom: 20px; text-align: center;">
                    <div style="font-size: 18px; font-weight: 600; color: #2d3748; margin-bottom: 4px;">${TrueBDCUtils.formatPhoneNumber(phoneNumber)}</div>
                    <div style="font-size: 12px; color: #718096;">Are you sure you want to call again?</div>
                </div>

                <div style="display: flex; gap: 12px;">
                    <button class="vinso-modal-cancel" style="flex: 1; padding: 12px 16px; border: 2px solid #e2e8f0; background: white; color: #4a5568; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; transition: all 0.2s;">
                        Cancel
                    </button>
                    <button class="vinso-modal-confirm" style="flex: 1; padding: 12px 16px; border: none; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; transition: all 0.2s;">
                        Call Now
                    </button>
                </div>
            `;

            // Add hover effects
            const cancelBtn = modal.querySelector('.vinso-modal-cancel');
            const confirmBtn = modal.querySelector('.vinso-modal-confirm');

            cancelBtn.addEventListener('mouseenter', () => {
                cancelBtn.style.backgroundColor = '#f7fafc';
                cancelBtn.style.borderColor = '#cbd5e0';
            });
            cancelBtn.addEventListener('mouseleave', () => {
                cancelBtn.style.backgroundColor = 'white';
                cancelBtn.style.borderColor = '#e2e8f0';
            });

            confirmBtn.addEventListener('mouseenter', () => {
                confirmBtn.style.transform = 'translateY(-1px)';
                confirmBtn.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.4)';
            });
            confirmBtn.addEventListener('mouseleave', () => {
                confirmBtn.style.transform = 'translateY(0)';
                confirmBtn.style.boxShadow = 'none';
            });

            // Event handlers
            const cleanup = () => {
                backdrop.style.opacity = '0';
                modal.style.transform = 'scale(0.9)';
                setTimeout(() => {
                    if (backdrop.parentNode) {
                        backdrop.parentNode.removeChild(backdrop);
                    }
                }, 300);
            };

            cancelBtn.addEventListener('click', () => {
                cleanup();
                resolve(false);
            });

            confirmBtn.addEventListener('click', () => {
                cleanup();
                resolve(true);
            });

            backdrop.addEventListener('click', (e) => {
                if (e.target === backdrop) {
                    cleanup();
                    resolve(false);
                }
            });

            // Show modal
            backdrop.appendChild(modal);
            document.body.appendChild(backdrop);

            // Animate in
            setTimeout(() => {
                backdrop.style.opacity = '1';
                modal.style.transform = 'scale(1)';
            }, 10);
        });
    }

    showModernModal(element, options) {
        const { type, title, message, phoneNumber, callCount, isConsecutive } = options;

        // Remove any existing modals
        const existingModal = document.querySelector('.vinso-modern-modal');
        if (existingModal) {
            existingModal.remove();
        }

        // Create modal container
        const modal = TrueBDCUtils.createElement('div', {
            class: 'vinso-modern-modal'
        }, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            backgroundColor: 'white',
            borderRadius: '12px',
            boxShadow: '0 10px 30px rgba(0, 0, 0, 0.15)',
            padding: '20px',
            maxWidth: '350px',
            width: 'auto',
            zIndex: '999999',
            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
            transform: 'translateX(100%)',
            transition: 'transform 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
            border: '1px solid #e2e8f0'
        });

        // Determine colors and icons based on type
        const typeConfig = {
            success: {
                color: '#667eea',
                bgColor: '#f0f4ff',
                icon: '📞',
                borderColor: '#667eea'
            },
            cancelled: {
                color: '#e53e3e',
                bgColor: '#fed7d7',
                icon: '❌',
                borderColor: '#e53e3e'
            },
            error: {
                color: '#e53e3e',
                bgColor: '#fed7d7',
                icon: '⚠️',
                borderColor: '#e53e3e'
            }
        };

        const config = typeConfig[type] || typeConfig.success;

        // Create modal content
        modal.innerHTML = `
            <div style="display: flex; align-items: flex-start; gap: 12px;">
                <div style="width: 40px; height: 40px; background: ${config.bgColor}; border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0; border: 2px solid ${config.borderColor};">
                    <span style="font-size: 18px;">${config.icon}</span>
                </div>
                <div style="flex: 1; min-width: 0;">
                    <h4 style="margin: 0 0 6px 0; color: #2d3748; font-size: 16px; font-weight: 600;">${title}</h4>
                    <p style="margin: 0 0 8px 0; color: #4a5568; font-size: 14px; line-height: 1.4; word-wrap: break-word;">${message}</p>
                    ${callCount ? `
                        <div style="display: flex; align-items: center; gap: 8px; margin-top: 12px;">
                            <div style="background: ${config.bgColor}; padding: 4px 8px; border-radius: 6px; font-size: 12px; color: ${config.color}; font-weight: 500;">
                                Call #${callCount}${isConsecutive ? ' (consecutive)' : ''}
                            </div>
                            ${callCount >= 2 ? `
                                <div style="font-size: 11px; color: #718096;">
                                    ${isConsecutive ? 'Quick succession' : 'Spaced calls'}
                                </div>
                            ` : ''}
                        </div>
                    ` : ''}
                </div>
                <button class="vinso-modal-close" style="background: none; border: none; color: #a0aec0; cursor: pointer; padding: 4px; border-radius: 4px; transition: color 0.2s;">
                    <span style="font-size: 16px;">×</span>
                </button>
            </div>
        `;

        // Add close functionality
        const closeBtn = modal.querySelector('.vinso-modal-close');
        closeBtn.addEventListener('click', () => {
            this.hideModal(modal);
        });

        closeBtn.addEventListener('mouseenter', () => {
            closeBtn.style.color = '#4a5568';
            closeBtn.style.backgroundColor = '#f7fafc';
        });

        closeBtn.addEventListener('mouseleave', () => {
            closeBtn.style.color = '#a0aec0';
            closeBtn.style.backgroundColor = 'transparent';
        });

        // Show modal
        document.body.appendChild(modal);

        // Animate in
        setTimeout(() => {
            modal.style.transform = 'translateX(0)';
        }, 10);

        // Auto-hide after delay
        setTimeout(() => {
            this.hideModal(modal);
        }, type === 'cancelled' ? 4000 : 3000);
    }

    hideModal(modal) {
        if (!modal || !modal.parentNode) return;

        modal.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        }, 400);
    }

    // Legacy tooltip method for backward compatibility
    showTooltip(element, text, type = 'info') {
        // Use modern modal for better UX
        this.showModernModal(element, {
            type: type === 'error' ? 'error' : 'success',
            title: type === 'error' ? 'Error' : 'Notification',
            message: text
        });
    }

    cleanPhoneNumber(phone) {
        if (!phone) return '';
        return phone.replace(/\D/g, '');
    }

    isValidPhoneNumber(cleanNumber) {
        // Must be 10 digits (US phone number)
        return /^\d{10}$/.test(cleanNumber);
    }

    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        TrueBDCUtils.log('VinSolutions Click to Call settings updated', newSettings);
    }

    onPageChange() {
        // Handle page changes in SPAs
        if (this.isVinSolutionsPage()) {
            if (!this.isActive) {
                this.init();
            } else {
                // Re-scan for phone numbers on new page
                setTimeout(() => {
                    this.scanForVinSoPhoneNumbers();
                }, 1000);
            }
        } else {
            if (this.isActive) {
                this.destroy();
            }
        }
    }

    destroy() {
        try {
            // Stop scanning
            this.stopScanning();

            // Stop observing
            if (this.observer) {
                this.observer.disconnect();
                this.observer = null;
            }

            // Clear intervals and timeouts
            if (this.scanTimeout) {
                clearTimeout(this.scanTimeout);
                this.scanTimeout = null;
            }

            // Remove all phone icons
            this.phoneIcons.forEach((icon) => {
                if (icon.parentNode) {
                    icon.parentNode.removeChild(icon);
                }
            });
            this.phoneIcons.clear();

            // Reset flags
            this.isActive = false;
            this.isProcessingComplete = false;
            this.observerSetup = false;

            console.log('VinSo Click-to-Call: Destroyed and cleaned up');
            TrueBDCUtils.log('VinSolutions Click to Call destroyed');
            TrueBDCUtils.logActivity('vinso_click_to_call_destroyed');
        } catch (error) {
            TrueBDCUtils.error('Error destroying VinSolutions Click to Call', error);
        }
    }

    // Static method to check if script should be available
    static isAvailable() {
        const url = window.location.href;
        return /vinsolutions\.app\.coxautoinc\.com|apps\.vinmanager\.com/i.test(url);
    }

    // Get current status
    getStatus() {
        return {
            isActive: this.isActive,
            isSupported: this.isVinSolutionsPage(),
            phoneIconsCount: this.phoneIcons.size,
            lastClickedNumber: this.lastClickedNumber,
            lastClickTime: this.lastClickTime
        };
    }
}

// Make class globally available
window.VinSoClickToCall = VinSoClickToCall;
