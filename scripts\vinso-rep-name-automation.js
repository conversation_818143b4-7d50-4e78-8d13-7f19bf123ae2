// TrueBDC CRM Automation Suite - VinSolutions Rep Name Automation
// Automatically inserts rep name and checks radio button on VinSolutions call logging pages

class VinSoRepNameAutomation {
    constructor(settings = {}) {
        this.settings = settings;
        this.isActive = false;
        this.retryLimit = 3;
        this.retryCount = 0;
        this.repName = '';
        this.frameContext = settings.frameContext || {};
        
        // Initialize the script
        this.init();
    }

    async init() {
        try {
            const url = window.location.href;
            const frameContext = this.frameContext.isIframe ? 'iframe' : 'main';
            
            TrueBDCUtils.log('VinSo Rep Name Automation initializing', {
                url: url,
                frameContext: frameContext
            });

            // Check if we're on a VinSolutions call logging page
            if (this.isVinSolutionsCallPage()) {
                await this.loadRepName();
                this.setupKeyboardShortcuts();
                this.startAutomation();
                this.isActive = true;

                TrueBDCUtils.log('VinSo Rep Name Automation activated', {
                    frameContext: frameContext,
                    url: url,
                    repName: this.repName
                });
                TrueBDCUtils.logActivity('vinso_rep_name_automation_activated', {
                    url: url,
                    frameContext: frameContext
                });
            } else {
                TrueBDCUtils.log('VinSo Rep Name Automation not activated - not call logging page', {
                    url: url,
                    frameContext: frameContext,
                    expectedPattern: 'vinsolutions.app.coxautoinc.com/cardashboard/pages/rims2.aspx'
                });
            }
        } catch (error) {
            TrueBDCUtils.error('Failed to initialize VinSo Rep Name Automation', error);
        }
    }

    isVinSolutionsCallPage() {
        const url = window.location.href;
        const callPagePatterns = [
            /vinsolutions\.app\.coxautoinc\.com\/cardashboard\/pages\/rims2\.aspx/i,
            /vinsolutions\.app\.coxautoinc\.com\/CarDashboard\/Pages\/LeadManagement\/LogCallV2\/LogCallV2\.aspx/i
        ];

        const isMatch = callPagePatterns.some(pattern => pattern.test(url));

        console.log('VinSo Call Page Check:', {
            url,
            patterns: callPagePatterns.map(p => p.toString()),
            isMatch
        });

        return isMatch;
    }

    async loadRepName() {
        try {
            // First priority: Check agent name from main settings
            const settingsResult = await chrome.storage.local.get('settings');
            if (settingsResult.settings && settingsResult.settings.agentName && settingsResult.settings.agentName.trim()) {
                this.repName = settingsResult.settings.agentName.trim();
                TrueBDCUtils.log('Rep name loaded from agent settings', { repName: this.repName });
                return;
            }

            // Second priority: VinSo-specific rep name storage
            const vinsoResult = await chrome.storage.local.get('vinso_rep_name');
            if (vinsoResult.vinso_rep_name) {
                this.repName = vinsoResult.vinso_rep_name;
                TrueBDCUtils.log('Rep name loaded from VinSo storage', { repName: this.repName });
                return;
            }

            // Third priority: Fallback to localStorage for compatibility
            const localStorageRepName = localStorage.getItem('repName');
            if (localStorageRepName) {
                this.repName = localStorageRepName;
                // Save to Chrome storage for future use
                await chrome.storage.local.set({ 'vinso_rep_name': this.repName });
                TrueBDCUtils.log('Rep name loaded from localStorage and saved to Chrome storage', { repName: this.repName });
                return;
            }

            // Default rep name
            this.repName = 'Rep';
            TrueBDCUtils.log('Using default rep name', { repName: this.repName });
        } catch (error) {
            TrueBDCUtils.error('Failed to load rep name', error);
            this.repName = 'Rep'; // Fallback
        }
    }

    async saveRepName(newRepName) {
        try {
            this.repName = newRepName;
            // Save to both Chrome storage and localStorage for compatibility
            await chrome.storage.local.set({ 'vinso_rep_name': newRepName });
            localStorage.setItem('repName', newRepName);
            TrueBDCUtils.log('Rep name saved', { repName: newRepName });
        } catch (error) {
            TrueBDCUtils.error('Failed to save rep name', error);
        }
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (event) => {
            // Ctrl+Alt+0 to change rep name
            if (event.ctrlKey && event.altKey && event.key === '0') {
                event.preventDefault();
                this.showRepNameModal();
            }
        });
    }

    showRepNameModal() {
        // Create modern modal for rep name input
        const modal = document.createElement('div');
        modal.className = 'truebdc-modal-overlay';
        modal.innerHTML = `
            <div class="truebdc-modal vinso-rep-modal">
                <div class="truebdc-modal-header">
                    <h3>Update Rep Name</h3>
                    <button class="truebdc-modal-close">&times;</button>
                </div>
                <div class="truebdc-modal-body">
                    <div class="input-group">
                        <label for="rep-name-input">Rep Name:</label>
                        <input type="text" id="rep-name-input" value="${this.repName}" placeholder="Enter rep name">
                    </div>
                    <div class="shortcut-info">
                        <small>💡 Use Ctrl+Alt+0 to quickly change rep name</small>
                        <br>
                        <small>ℹ️ This will be inserted as "{name} -- Calling" in call notes</small>
                        <br>
                        <small>⚙️ You can also set Agent Name in Settings tab for automatic use</small>
                    </div>
                </div>
                <div class="truebdc-modal-footer">
                    <button class="truebdc-btn truebdc-btn-secondary" id="cancel-rep-name">Cancel</button>
                    <button class="truebdc-btn truebdc-btn-primary" id="save-rep-name">Save</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Focus input and select text
        const input = modal.querySelector('#rep-name-input');
        setTimeout(() => {
            input.focus();
            input.select();
        }, 100);

        // Handle save
        const saveBtn = modal.querySelector('#save-rep-name');
        const cancelBtn = modal.querySelector('#cancel-rep-name');
        const closeBtn = modal.querySelector('.truebdc-modal-close');

        const handleSave = async () => {
            const newRepName = input.value.trim();
            if (newRepName) {
                await this.saveRepName(newRepName);
                this.showSuccessNotification(`Rep name updated to: ${newRepName}`);
            }
            modal.remove();
        };

        const handleCancel = () => {
            modal.remove();
        };

        saveBtn.addEventListener('click', handleSave);
        cancelBtn.addEventListener('click', handleCancel);
        closeBtn.addEventListener('click', handleCancel);

        // Handle Enter key
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                handleSave();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                handleCancel();
            }
        });

        // Close on overlay click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                handleCancel();
            }
        });
    }

    startAutomation() {
        // Start the automation process with a delay
        setTimeout(() => {
            this.insertTextAndCheckRadio();
        }, 1000); // 1 second delay to ensure page is loaded
    }

    insertTextAndCheckRadio() {
        // First, try to insert text in the call notes textarea
        this.insertTextWithRetries();

        // Then set the "No" radio button as default (only if not already set by user)
        this.setDefaultRadioButton();
    }

    insertTextWithRetries() {
        const callNotesTextarea = document.querySelector('#CallLoggingCallNotes');

        if (callNotesTextarea) {
            const textToInsert = `${this.repName} -- Calling`;

            // Only insert if textarea is empty to avoid overwriting user input
            if (!callNotesTextarea.value.trim()) {
                callNotesTextarea.value = textToInsert;

                // Trigger events to ensure the change is registered
                const inputEvent = new Event('input', { bubbles: true, cancelable: true });
                callNotesTextarea.dispatchEvent(inputEvent);

                const changeEvent = new Event('change', { bubbles: true, cancelable: true });
                callNotesTextarea.dispatchEvent(changeEvent);

                TrueBDCUtils.log('Text inserted successfully in call notes', { text: textToInsert });
                this.showSuccessNotification(`Inserted: ${textToInsert}`);
            } else {
                TrueBDCUtils.log('Call notes textarea already has content, skipping insertion');
            }
        } else if (this.retryCount < this.retryLimit) {
            TrueBDCUtils.log(`Call notes textarea not found, retrying... (${this.retryCount + 1}/${this.retryLimit})`);
            this.retryCount++;
            setTimeout(() => {
                this.insertTextWithRetries();
            }, 1000); // Retry after 1 second
        } else {
            TrueBDCUtils.error('Call notes textarea element not found, retries exceeded');
            this.showErrorNotification('Could not find call notes textarea');
        }
    }

    setDefaultRadioButton() {
        // Look for the "No" radio button and set it as default
        const noRadio = document.querySelector('#CallLoggingCustomerContactedCode-No');

        if (noRadio && !noRadio.checked) {
            // Check if any radio button is already selected (user might have changed it)
            const anyRadioChecked = document.querySelector('input[name="CustomerContactedCode"]:checked');

            if (!anyRadioChecked) {
                // No radio button selected yet, set "No" as default
                noRadio.click();
                TrueBDCUtils.log('No radio button set as default');
                this.showSuccessNotification('Set "No" as default for customer contacted');
            } else {
                TrueBDCUtils.log('Radio button already selected by user, keeping user choice');
            }
        } else if (noRadio && noRadio.checked) {
            TrueBDCUtils.log('No radio button already selected');
        } else {
            TrueBDCUtils.log('No radio button not found, trying alternative selector');
            // Try alternative selector
            const altNoRadio = document.querySelector('input[name="CustomerContactedCode"][value="N"]');
            if (altNoRadio && !altNoRadio.checked) {
                const anyRadioChecked = document.querySelector('input[name="CustomerContactedCode"]:checked');
                if (!anyRadioChecked) {
                    altNoRadio.click();
                    TrueBDCUtils.log('No radio button set as default (alternative selector)');
                    this.showSuccessNotification('Set "No" as default for customer contacted');
                }
            }
        }
    }

    showSuccessNotification(message) {
        TrueBDCUtils.showNotification(message, 'success', 3000);
    }

    showErrorNotification(message) {
        TrueBDCUtils.showNotification(message, 'error', 5000);
    }

    async updateSettings(newSettings) {
        // Update settings and reload rep name if agent name changed
        if (newSettings && newSettings.agentName !== this.settings.agentName) {
            this.settings = { ...this.settings, ...newSettings };
            await this.loadRepName();
            TrueBDCUtils.log('Rep name updated from settings change', {
                newRepName: this.repName,
                agentName: newSettings.agentName
            });
        }
    }

    destroy() {
        try {
            this.isActive = false;
            this.retryCount = 0;

            // Remove any event listeners if needed
            // (keyboard shortcuts are on document, so they'll persist)

            TrueBDCUtils.log('VinSo Rep Name Automation destroyed');
            TrueBDCUtils.logActivity('vinso_rep_name_automation_destroyed');
        } catch (error) {
            TrueBDCUtils.error('Error destroying VinSo Rep Name Automation', error);
        }
    }

    // Static method to check if script should be available
    static isAvailable() {
        const url = window.location.href;
        return /vinsolutions\.app\.coxautoinc\.com/i.test(url);
    }
}

// Make the class globally available
window.VinSoRepNameAutomation = VinSoRepNameAutomation;
