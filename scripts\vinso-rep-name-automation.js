// TrueBDC CRM Automation Suite - VinSolutions Rep Name Automation
// Automatically inserts rep name and checks radio button on VinSolutions call logging pages

class VinSoRepNameAutomation {
    constructor(settings = {}) {
        this.settings = settings;
        this.isActive = false;
        this.retryLimit = 3;
        this.retryCount = 0;
        this.repName = '';
        this.frameContext = settings.frameContext || {};
        
        // Initialize the script
        this.init();
    }

    async init() {
        try {
            const url = window.location.href;
            const frameContext = this.frameContext.isIframe ? 'iframe' : 'main';
            
            TrueBDCUtils.log('VinSo Rep Name Automation initializing', {
                url: url,
                frameContext: frameContext
            });

            // Check if we're on a VinSolutions call logging page
            if (this.isVinSolutionsCallPage()) {
                await this.loadRepName();
                this.setupKeyboardShortcuts();
                this.startAutomation();
                this.isActive = true;

                TrueBDCUtils.log('VinSo Rep Name Automation activated', {
                    frameContext: frameContext,
                    url: url,
                    repName: this.repName
                });
                TrueBDCUtils.logActivity('vinso_rep_name_automation_activated', {
                    url: url,
                    frameContext: frameContext
                });
            } else {
                TrueBDCUtils.log('VinSo Rep Name Automation not activated - not call logging page', {
                    url: url,
                    frameContext: frameContext,
                    expectedPattern: 'vinsolutions.app.coxautoinc.com/cardashboard/pages/rims2.aspx'
                });
            }
        } catch (error) {
            TrueBDCUtils.error('Failed to initialize VinSo Rep Name Automation', error);
        }
    }

    isVinSolutionsCallPage() {
        const url = window.location.href;
        const callPagePatterns = [
            /vinsolutions\.app\.coxautoinc\.com\/cardashboard\/pages\/rims2\.aspx/i,
            /vinsolutions\.app\.coxautoinc\.com\/CarDashboard\/Pages\/LeadManagement\/LogCallV2\/LogCallV2\.aspx/i
        ];

        const isMatch = callPagePatterns.some(pattern => pattern.test(url));

        console.log('VinSo Call Page Check:', {
            url,
            patterns: callPagePatterns.map(p => p.toString()),
            isMatch
        });

        return isMatch;
    }

    async loadRepName() {
        try {
            // Try to get rep name from Chrome storage first
            const result = await chrome.storage.local.get('vinso_rep_name');
            if (result.vinso_rep_name) {
                this.repName = result.vinso_rep_name;
                TrueBDCUtils.log('Rep name loaded from Chrome storage', { repName: this.repName });
                return;
            }

            // Fallback to localStorage for compatibility
            const localStorageRepName = localStorage.getItem('repName');
            if (localStorageRepName) {
                this.repName = localStorageRepName;
                // Save to Chrome storage for future use
                await chrome.storage.local.set({ 'vinso_rep_name': this.repName });
                TrueBDCUtils.log('Rep name loaded from localStorage and saved to Chrome storage', { repName: this.repName });
                return;
            }

            // Default rep name
            this.repName = 'Rep';
            TrueBDCUtils.log('Using default rep name', { repName: this.repName });
        } catch (error) {
            TrueBDCUtils.error('Failed to load rep name', error);
            this.repName = 'Rep'; // Fallback
        }
    }

    async saveRepName(newRepName) {
        try {
            this.repName = newRepName;
            // Save to both Chrome storage and localStorage for compatibility
            await chrome.storage.local.set({ 'vinso_rep_name': newRepName });
            localStorage.setItem('repName', newRepName);
            TrueBDCUtils.log('Rep name saved', { repName: newRepName });
        } catch (error) {
            TrueBDCUtils.error('Failed to save rep name', error);
        }
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (event) => {
            // Ctrl+Alt+0 to change rep name
            if (event.ctrlKey && event.altKey && event.key === '0') {
                event.preventDefault();
                this.showRepNameModal();
            }
        });
    }

    showRepNameModal() {
        // Create modern modal for rep name input
        const modal = document.createElement('div');
        modal.className = 'truebdc-modal-overlay';
        modal.innerHTML = `
            <div class="truebdc-modal vinso-rep-modal">
                <div class="truebdc-modal-header">
                    <h3>Update Rep Name</h3>
                    <button class="truebdc-modal-close">&times;</button>
                </div>
                <div class="truebdc-modal-body">
                    <div class="input-group">
                        <label for="rep-name-input">Rep Name:</label>
                        <input type="text" id="rep-name-input" value="${this.repName}" placeholder="Enter rep name">
                    </div>
                    <div class="shortcut-info">
                        <small>💡 Use Ctrl+Alt+0 to quickly change rep name</small>
                    </div>
                </div>
                <div class="truebdc-modal-footer">
                    <button class="truebdc-btn truebdc-btn-secondary" id="cancel-rep-name">Cancel</button>
                    <button class="truebdc-btn truebdc-btn-primary" id="save-rep-name">Save</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Focus input and select text
        const input = modal.querySelector('#rep-name-input');
        setTimeout(() => {
            input.focus();
            input.select();
        }, 100);

        // Handle save
        const saveBtn = modal.querySelector('#save-rep-name');
        const cancelBtn = modal.querySelector('#cancel-rep-name');
        const closeBtn = modal.querySelector('.truebdc-modal-close');

        const handleSave = async () => {
            const newRepName = input.value.trim();
            if (newRepName) {
                await this.saveRepName(newRepName);
                this.showSuccessNotification(`Rep name updated to: ${newRepName}`);
            }
            modal.remove();
        };

        const handleCancel = () => {
            modal.remove();
        };

        saveBtn.addEventListener('click', handleSave);
        cancelBtn.addEventListener('click', handleCancel);
        closeBtn.addEventListener('click', handleCancel);

        // Handle Enter key
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                handleSave();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                handleCancel();
            }
        });

        // Close on overlay click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                handleCancel();
            }
        });
    }

    startAutomation() {
        // Start the automation process with a delay
        setTimeout(() => {
            this.insertTextAndCheckRadio();
        }, 1000); // 1 second delay to ensure page is loaded
    }

    insertTextAndCheckRadio() {
        // First, try to insert text in the phone input field
        this.insertTextWithRetries();
        
        // Then check the "Yes" radio button
        this.checkYesRadioButton();
    }

    insertTextWithRetries() {
        const phoneInput = document.querySelector('#PhoneInputChildren-maskedInput');
        
        if (phoneInput) {
            const textToInsert = `${this.repName} -- Calling`;
            phoneInput.value = textToInsert;

            // Trigger events to ensure the change is registered
            const inputEvent = new Event('input', { bubbles: true, cancelable: true });
            phoneInput.dispatchEvent(inputEvent);

            const changeEvent = new Event('change', { bubbles: true, cancelable: true });
            phoneInput.dispatchEvent(changeEvent);

            TrueBDCUtils.log('Text inserted successfully in phone input', { text: textToInsert });
            this.showSuccessNotification(`Inserted: ${textToInsert}`);
        } else if (this.retryCount < this.retryLimit) {
            TrueBDCUtils.log(`Phone input not found, retrying... (${this.retryCount + 1}/${this.retryLimit})`);
            this.retryCount++;
            setTimeout(() => {
                this.insertTextWithRetries();
            }, 1000); // Retry after 1 second
        } else {
            TrueBDCUtils.error('Phone input element not found, retries exceeded');
            this.showErrorNotification('Could not find phone input field');
        }
    }

    checkYesRadioButton() {
        // Look for the "Yes" radio button
        const yesRadio = document.querySelector('#CallLoggingCustomerContactedCode-Yes');
        
        if (yesRadio && !yesRadio.checked) {
            yesRadio.click();
            TrueBDCUtils.log('Yes radio button checked successfully');
            this.showSuccessNotification('Selected "Yes" for customer contacted');
        } else if (yesRadio && yesRadio.checked) {
            TrueBDCUtils.log('Yes radio button already checked');
        } else {
            TrueBDCUtils.log('Yes radio button not found');
            // Try alternative selector
            const altYesRadio = document.querySelector('input[name="CustomerContactedCode"][value="Y"]');
            if (altYesRadio && !altYesRadio.checked) {
                altYesRadio.click();
                TrueBDCUtils.log('Yes radio button checked successfully (alternative selector)');
                this.showSuccessNotification('Selected "Yes" for customer contacted');
            }
        }
    }

    showSuccessNotification(message) {
        TrueBDCUtils.showNotification(message, 'success', 3000);
    }

    showErrorNotification(message) {
        TrueBDCUtils.showNotification(message, 'error', 5000);
    }

    destroy() {
        try {
            this.isActive = false;
            this.retryCount = 0;

            // Remove any event listeners if needed
            // (keyboard shortcuts are on document, so they'll persist)

            TrueBDCUtils.log('VinSo Rep Name Automation destroyed');
            TrueBDCUtils.logActivity('vinso_rep_name_automation_destroyed');
        } catch (error) {
            TrueBDCUtils.error('Error destroying VinSo Rep Name Automation', error);
        }
    }

    // Static method to check if script should be available
    static isAvailable() {
        const url = window.location.href;
        return /vinsolutions\.app\.coxautoinc\.com/i.test(url);
    }
}

// Make the class globally available
window.VinSoRepNameAutomation = VinSoRepNameAutomation;
